<template>
  <div style="
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: black;
    display: flex;
    justify-content: center;
    align-items: center;
  ">
    <div style="
      width: 375px;
      height: 100vh;
      background-color: white;
      border-radius: 0.5rem;
      padding: 1.5rem;
    ">
      <h1 style="
        font-size: 1.2rem;
        font-weight: bold;
        text-align: center;
        color: #6b7280;
      ">MossBets(Pragmantic Test)</h1>   
      <div v-if="loading" style="text-align: center;">
        <p style="color: #6b7280;">Launching...</p>
      </div>
      <div v-else-if="error" style="text-align: center; color: red;">
        <p>{{ error }}</p>
      </div>
      <div v-else-if="launchSuccess" style="text-align: center; color: green;">
        <p>Launch Successful!</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import axios from 'axios'
import { ref, onMounted } from 'vue'

const loading = ref(true)
const error = ref(null)
const launchSuccess = ref(false)

const launchApp = async () => {
  loading.value = true
  error.value = null
  launchSuccess.value = false

  const headers = {
    'x-hash-key': 'cd440474aeff2f823f7a8b414c5c8438',
    'X-app-Key': '{zFjt5InE02-vDz0AfPTSxXJ9hjZ1bvAYNrn5iue2Z47-ZVOeuOaEXD',
    'x-access-key': 'ZjNlazkzTUNQZkR2cjA0ejhMamZjZz09OjrFeB+MBJdESX7gcWl/637N'
  }

  const payload = {
    mode: 1,
    game_id: 1,
    timestamp: Math.floor(Date.now() / 1000),
    promo: ''
  }

  // try {
  //   const response = await axios.post('https://dev.v1.mossbets.be/pp/launch', payload, { headers })
  //   console.log('Launch successful:', response.data)
  //   launchSuccess.value = true
  // } catch (err) {
  //   console.error('Launch failed:', err)
  //   error.value = err.message || 'Launch failed. Please try again.'
  // } finally {
  //   loading.value = false
  // }
}

// Auto-launch when page loads
onMounted(() => {
  launchApp()
})
</script>

<style scoped>
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
