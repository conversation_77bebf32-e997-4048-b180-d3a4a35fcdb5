<template>
  <div style="
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: black;
    display: flex;
    justify-content: center;
    align-items: center;
  ">
    <div style="
      width: 100%;
      height: 100vh;
      background-color: white;
      display: flex;
      flex-direction: column;
    ">
      <div style="
        padding: 1rem;
        background-color: #f3f4f6;
        border-bottom: 1px solid #e5e7eb;
      ">
        <h1 style="
          font-size: 1.2rem;
          font-weight: bold;
          text-align: center;
          color: #6b7280;
          margin: 0;
        ">MossBets - Aviator Game</h1>
        <button
          @click="launchGame"
          :disabled="loading"
          style="
            margin-top: 0.5rem;
            padding: 0.5rem 1rem;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 0.25rem;
            cursor: pointer;
            display: block;
            margin-left: auto;
            margin-right: auto;
          "
        >
          {{ loading ? 'Loading...' : 'Launch Game' }}
        </button>
      </div>

      <div style="flex: 1; overflow: hidden;">
        <div v-if="loading && !gameContent" style="
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
        ">
          <p style="color: #6b7280; font-size: 1.1rem;">Launching game...</p>
        </div>

        <div v-else-if="error" style="
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
          text-align: center;
          padding: 2rem;
        ">
          <div>
            <p style="color: #dc2626; font-size: 1.1rem; margin-bottom: 1rem;">{{ error }}</p>
            <button
              @click="launchGame"
              style="
                padding: 0.5rem 1rem;
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 0.25rem;
                cursor: pointer;
              "
            >
              Try Again
            </button>
          </div>
        </div>

        <div v-else-if="gameContent" style="
          width: 100%;
          height: 100%;
        " v-html="gameContent">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import axios from 'axios'
import { ref, onMounted } from 'vue'

const loading = ref(false)
const error = ref(null)
const gameContent = ref(null)

const launchGame = async () => {
  loading.value = true
  error.value = null
  gameContent.value = null

  const headers = {
    'x-access-key': 'QncwcDdzRm12TkZTNWc4S21zUnZCUT09OjqI5paNBPUXWAiEWE1/DP+s',
    'X-app-Key': 'zFjt5InE02-vDz0AfPTSxXJ9hjZ1bvAYNrn5iue2Z47-ZVOeuOaEXD',
    'Content-Type': 'application/json'
  }

  const payload = {
    game_id: "895/aviator",
    mode: 1,
    provider_id: "895"
  }

  try {
    console.log('Launching game with payload:', payload)
    const response = await axios.post('https://dev.v1.api.mossbets.bet/soft/v1/launch', payload, { headers })

    console.log('API Response:', response.data)

    if (response.data && response.data.data && response.data.data.response && response.data.data.response.response) {
      const htmlContent = response.data.data.response.response
      // Extract the HTML content after the status code
      const htmlMatch = htmlContent.match(/^1,(.*)$/s)
      if (htmlMatch) {
        gameContent.value = htmlMatch[1]
        console.log('Game content loaded successfully')
      } else {
        gameContent.value = htmlContent
      }
    } else {
      throw new Error('Invalid response format from API')
    }
  } catch (err) {
    console.error('Launch failed:', err)
    if (err.response) {
      error.value = `API Error: ${err.response.status} - ${err.response.data?.message || err.response.statusText}`
    } else if (err.request) {
      error.value = 'Network error: Unable to reach the API'
    } else {
      error.value = err.message || 'Launch failed. Please try again.'
    }
  } finally {
    loading.value = false
  }
}

// Auto-launch when page loads
onMounted(() => {
  launchGame()
})
</script>

<style scoped>
/* Game container styles */
button:hover:not(:disabled) {
  background-color: #2563eb !important;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
