<template>
  <div class="app-container">
    <!-- Aviation Background -->
    <div class="sky-background">
      <div class="clouds cloud-1"></div>
      <div class="clouds cloud-2"></div>
      <div class="clouds cloud-3"></div>
      <div class="plane-icon">✈️</div>
    </div>

    <div class="main-content">
      <!-- Header -->
      <header class="aviation-header">
        <div class="header-content">
          <div class="logo-section">
            <h1 class="title">MossBets Aviator</h1>
          </div>
          <button
            @click="launchGame"
            :disabled="loading"
            class="launch-button"
            :class="{ 'loading': loading }"
          >
            <span v-if="loading" class="loading-content">
              <span class="spinner"></span>
              Preparing for Takeoff...
            </span>
            <span v-else class="launch-content">
              <span class="rocket">🚀</span>
              Launch Flight
              <span class="rocket">🚀</span>
            </span>
          </button>
        </div>
      </header>

      <!-- Game Content Area -->
      <main class="game-area">
        <!-- Loading State -->
        <div v-if="loading && !gameContent" class="loading-screen">
          <div class="loading-animation">
            <div class="plane-loading">✈️</div>
            <div class="runway"></div>
          </div>
          <p class="loading-text">Preparing aircraft for departure...</p>
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="error-screen">
          <div class="error-content">
            <div class="error-icon">🛑</div>
            <h3 class="error-title">Flight Delayed</h3>
            <p class="error-message">{{ error }}</p>
            <button
              @click="launchGame"
              class="retry-button"
            >
              <span class="retry-icon">🔄</span>
              Retry Takeoff
            </button>
          </div>
        </div>

        <!-- Game Content -->
        <div v-else-if="gameContent" class="game-container" ref="gameContainerRef">
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import axios from 'axios'
import { ref, onMounted } from 'vue'

const loading = ref(false)
const error = ref(null)
const gameContent = ref(null)
const gameContainerRef = ref(null)

// Function to properly insert HTML content and execute scripts
const insertGameContent = (htmlContent) => {
  if (!gameContainerRef.value) {
    console.error('Game container ref not found')
    return
  }

  // Clear existing content
  gameContainerRef.value.innerHTML = ''

  // Handle different response formats
  let cleanHtml = htmlContent

  // Check if content starts with status code (like "1,")
  const statusCodeMatch = htmlContent.match(/^(\d+),(.*)$/s)
  if (statusCodeMatch) {
    cleanHtml = statusCodeMatch[2]
    console.log('Extracted HTML content after status code:', statusCodeMatch[1])
  }

  // Clean up escaped characters from JSON
  cleanHtml = cleanHtml
    .replace(/\\r\\n/g, '\n')
    .replace(/\\n/g, '\n')
    .replace(/\\"/g, '"')
    .replace(/\\\//g, '/')

  console.log('Cleaned HTML content:', cleanHtml.substring(0, 200) + '...')

  // Create a temporary container to parse the HTML
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = cleanHtml

  // Extract and handle script tags separately
  const scripts = tempDiv.querySelectorAll('script')
  const scriptContents = []

  scripts.forEach(script => {
    if (script.src) {
      // External script
      scriptContents.push({ type: 'external', src: script.src })
    } else {
      // Inline script
      scriptContents.push({ type: 'inline', content: script.textContent || script.innerHTML })
    }
    script.remove() // Remove from temp div
  })

  // Insert the HTML content (without scripts)
  while (tempDiv.firstChild) {
    gameContainerRef.value.appendChild(tempDiv.firstChild)
  }

  // Execute scripts in order
  scriptContents.forEach((scriptInfo, index) => {
    const newScript = document.createElement('script')
    if (scriptInfo.type === 'external') {
      newScript.src = scriptInfo.src
      newScript.async = false
      console.log(`Loading external script ${index + 1}:`, scriptInfo.src)
    } else {
      newScript.textContent = scriptInfo.content
      console.log(`Executing inline script ${index + 1}:`, scriptInfo.content.substring(0, 100) + '...')
    }
    document.head.appendChild(newScript)
  })

  console.log('Game content inserted and scripts executed')
  console.log('Scripts found and executed:', scriptContents.length)
  gameContent.value = 'loaded' // Set to indicate content is loaded
}

const launchGame = async () => {
  loading.value = true
  error.value = null
  gameContent.value = null

  const headers = {
    'x-access-key': 'RlI5dmJmcjd0U0hhL3g2MHpadld5UT09OjqHh+cziwCpOojAoVZWOiUu',
    'X-app-Key': 'zFjt5InE02-vDz0AfPTSxXJ9hjZ1bvAYNrn5iue2Z47-ZVOeuOaEXD',
    'Content-Type': 'application/json'
  }

  const payload = {
    game_id: "895/aviator",
    mode: 0,
    provider_id: "895"
  }

  try {
    console.log('Launching game with payload:', payload)
    const response = await axios.post('https://dev.v1.api.mossbets.bet/soft/v1/launch', payload, { headers })

    console.log('API Response:', response.data)

    if (response.data && response.data.data && response.data.data.response && response.data.data.response.response) {
      const htmlContent = response.data.data.response.response
      console.log('Raw HTML content received:', htmlContent.substring(0, 200) + '...')

      // Use our custom function to insert and execute the content
      insertGameContent(htmlContent)
    } else {
      throw new Error('Invalid response format from API')
    }
  } catch (err) {
    console.error('Launch failed:', err)
    if (err.response) {
      error.value = `API Error: ${err.response.status} - ${err.response.data?.message || err.response.statusText}`
    } else if (err.request) {
      error.value = 'Network error: Unable to reach the API'
    } else {
      error.value = err.message || 'Launch failed. Please try again.'
    }
  } finally {
    loading.value = false
  }
}

// Auto-launch when page loads
onMounted(() => {
  launchGame()
})
</script>

<style scoped>
/* Aviation Theme Styles */
.app-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  font-family: 'Arial', sans-serif;
}

/* Sky Background */
.sky-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg,
    #87CEEB 0%,     /* Sky blue */
    #98D8E8 25%,    /* Light blue */
    #B0E0E6 50%,    /* Powder blue */
    #E0F6FF 75%,    /* Very light blue */
    #F0F8FF 100%    /* Alice blue */
  );
  z-index: 1;
}

/* Animated Clouds */
.clouds {
  position: absolute;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50px;
  opacity: 0.7;
}

.clouds::before,
.clouds::after {
  content: '';
  position: absolute;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50px;
}

.cloud-1 {
  width: 80px;
  height: 40px;
  top: 20%;
  left: 10%;
  animation: float 20s infinite linear;
}

.cloud-1::before {
  width: 50px;
  height: 50px;
  top: -25px;
  left: 10px;
}

.cloud-1::after {
  width: 60px;
  height: 40px;
  top: -15px;
  right: 10px;
}

.cloud-2 {
  width: 60px;
  height: 30px;
  top: 40%;
  right: 20%;
  animation: float 25s infinite linear reverse;
}

.cloud-2::before {
  width: 40px;
  height: 40px;
  top: -20px;
  left: 5px;
}

.cloud-2::after {
  width: 50px;
  height: 30px;
  top: -10px;
  right: 5px;
}

.cloud-3 {
  width: 100px;
  height: 50px;
  top: 60%;
  left: 60%;
  animation: float 30s infinite linear;
}

.cloud-3::before {
  width: 60px;
  height: 60px;
  top: -30px;
  left: 15px;
}

.cloud-3::after {
  width: 70px;
  height: 50px;
  top: -20px;
  right: 15px;
}

@keyframes float {
  0% { transform: translateX(-100px); }
  100% { transform: translateX(calc(100vw + 100px)); }
}

/* Floating Plane */
.plane-icon {
  position: absolute;
  top: 15%;
  right: -50px;
  font-size: 2rem;
  animation: fly 15s infinite linear;
  z-index: 2;
}

@keyframes fly {
  0% {
    transform: translateX(0) translateY(0) rotate(0deg);
    right: -50px;
  }
  25% {
    transform: translateX(-25vw) translateY(-20px) rotate(-5deg);
  }
  50% {
    transform: translateX(-50vw) translateY(10px) rotate(2deg);
  }
  75% {
    transform: translateX(-75vw) translateY(-15px) rotate(-3deg);
  }
  100% {
    transform: translateX(-100vw) translateY(0) rotate(0deg);
    right: calc(100vw + 50px);
  }
}

/* Main Content */
.main-content {
  position: relative;
  z-index: 3;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.aviation-header {
  background: linear-gradient(135deg,
    rgba(30, 58, 138, 0.95) 0%,    /* Dark blue */
    rgba(59, 130, 246, 0.95) 50%,  /* Blue */
    rgba(147, 197, 253, 0.95) 100% /* Light blue */
  );
  backdrop-filter: blur(10px);
  border-bottom: 3px solid #fbbf24;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 1rem;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.plane-emoji {
  font-size: 2rem;
  animation: bounce 2s infinite;
}

.plane-emoji.reverse {
  transform: scaleX(-1);
  animation: bounce 2s infinite 0.5s;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0) scaleX(1); }
  40% { transform: translateY(-10px) scaleX(1); }
  60% { transform: translateY(-5px) scaleX(1); }
}

.plane-emoji.reverse {
  transform: scaleX(-1);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.title {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: bold;
  color: white;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.subtitle {
  color: #fbbf24;
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  margin: 0.5rem 0 1.5rem 0;
  font-style: italic;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Launch Button */
.launch-button {
  background: linear-gradient(45deg, #f59e0b, #fbbf24, #f59e0b);
  background-size: 200% 200%;
  color: #1f2937;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
  text-transform: uppercase;
  letter-spacing: 1px;
  animation: shimmer 3s infinite;
  min-width: 200px;
}

.launch-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(245, 158, 11, 0.6);
  background-position: 100% 100%;
}

.launch-button:active {
  transform: translateY(-1px);
}

.launch-button:disabled {
  opacity: 0.8;
  cursor: not-allowed;
  transform: none;
}

.launch-button.loading {
  background: linear-gradient(45deg, #6b7280, #9ca3af, #6b7280);
  animation: pulse 2s infinite;
}

@keyframes shimmer {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.loading-content, .launch-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #1f2937;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.rocket {
  animation: rocket-bounce 1s infinite alternate;
}

@keyframes rocket-bounce {
  0% { transform: translateY(0); }
  100% { transform: translateY(-5px); }
}

/* Game Area */
.game-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow: hidden;
}

/* Loading Screen */
.loading-screen {
  text-align: center;
  color: #1f2937;
}

.loading-animation {
  position: relative;
  width: 200px;
  height: 100px;
  margin: 0 auto 2rem;
}

.plane-loading {
  font-size: 3rem;
  position: absolute;
  top: 20px;
  left: 0;
  animation: taxi 3s infinite;
}

@keyframes taxi {
  0% { left: 0; transform: rotate(0deg); }
  50% { left: calc(100% - 60px); transform: rotate(5deg); }
  100% { left: 0; transform: rotate(0deg); }
}

.runway {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: repeating-linear-gradient(
    90deg,
    #374151 0px,
    #374151 20px,
    transparent 20px,
    transparent 40px
  );
  border-radius: 2px;
}

.loading-text {
  font-size: clamp(1rem, 3vw, 1.3rem);
  font-weight: bold;
  margin-bottom: 1rem;
  color: #1e40af;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.loading-dots span {
  width: 12px;
  height: 12px;
  background: #3b82f6;
  border-radius: 50%;
  animation: dot-bounce 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes dot-bounce {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1.2); opacity: 1; }
}

/* Error Screen */
.error-screen {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
  padding: 2rem;
}

.error-content {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: shake 0.5s infinite alternate;
}

@keyframes shake {
  0% { transform: translateX(0); }
  100% { transform: translateX(-5px); }
}

.error-title {
  color: #dc2626;
  font-size: clamp(1.2rem, 4vw, 1.8rem);
  font-weight: bold;
  margin-bottom: 1rem;
}

.error-message {
  color: #374151;
  font-size: clamp(0.9rem, 2.5vw, 1.1rem);
  margin-bottom: 2rem;
  line-height: 1.5;
}

.retry-button {
  background: linear-gradient(45deg, #dc2626, #ef4444);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-size: clamp(0.9rem, 2.5vw, 1rem);
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(220, 38, 38, 0.4);
}

.retry-icon {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Game Container */
.game-container {
  width: 100%;
  height: 100%;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  background: white;
  position: relative;
}

/* Ensure iframe and game content fills container properly */
.game-container :deep(#egamings_container) {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
}

.game-container :deep(iframe) {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  border-radius: 15px;
  display: block !important;
}

/* Additional game styling */
.game-container :deep(div) {
  width: 100%;
  height: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .aviation-header {
    padding: 0.8rem;
  }

  .logo-section {
    flex-direction: column;
    gap: 0.5rem;
  }

  .plane-emoji {
    font-size: 1.5rem;
  }

  .launch-button {
    padding: 0.8rem 1.5rem;
    min-width: 180px;
  }

  .game-area {
    padding: 0.5rem;
  }

  .loading-animation {
    width: 150px;
    height: 80px;
  }

  .plane-loading {
    font-size: 2rem;
  }

  .error-content {
    padding: 1.5rem;
    margin: 0 1rem;
  }

  .clouds {
    display: none; /* Hide clouds on mobile for better performance */
  }
}

@media (max-width: 480px) {
  .main-content {
    height: 100vh;
  }

  .aviation-header {
    padding: 0.5rem;
  }

  .launch-button {
    padding: 0.7rem 1.2rem;
    min-width: 160px;
    font-size: 0.9rem;
  }

  .game-area {
    padding: 0.25rem;
  }

  .plane-icon {
    display: none; /* Hide flying plane on very small screens */
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .sky-background {
    background-attachment: fixed;
  }
}

/* Landscape Mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .aviation-header {
    padding: 0.5rem;
  }

  .logo-section {
    flex-direction: row;
    gap: 0.5rem;
  }

  .subtitle {
    margin: 0.25rem 0 0.75rem 0;
  }

  .launch-button {
    padding: 0.5rem 1rem;
  }
}
</style>
