{"name": "mossbets-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.9.0", "vue": "^3.5.14"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^4.1.6", "vite": "^6.3.5"}, "description": "This template should help get you started developing with Vue 3 in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.", "main": "vite.config.js", "keywords": [], "author": "", "license": "ISC"}